/**
 * Sepet Sidebar JavaScript
 */
document.addEventListener('DOMContentLoaded', function() {
    const cartToggle = document.getElementById('cart-toggle');
    const cartSidebar = document.getElementById('cart-sidebar');
    const cartSidebarClose = document.getElementById('cart-sidebar-close');
    const cartSidebarOverlay = document.querySelector('.cart-sidebar-overlay');

    // Sepet sidebar'ini ac
    function openCartSidebar() {
        cartSidebar.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // Sepet sidebar'ini kapat
    function closeCartSidebar() {
        cartSidebar.classList.remove('active');
        document.body.style.overflow = '';
    }

    // Event listener'lar
    if (cartToggle) {
        cartToggle.addEventListener('click', function(e) {
            e.preventDefault();
            openCartSidebar();
        });
    }

    if (cartSidebarClose) {
        cartSidebarClose.addEventListener('click', function(e) {
            e.preventDefault();
            closeCartSidebar();
        });
    }

    if (cartSidebarOverlay) {
        cartSidebarOverlay.addEventListener('click', function(e) {
            e.preventDefault();
            closeCartSidebar();
        });
    }

    // ESC tusuna basildiginda sidebar'i kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && cartSidebar.classList.contains('active')) {
            closeCartSidebar();
        }
    });

    // WooCommerce AJAX sepet guncelleme olaylarini dinle
    jQuery(document.body).on('added_to_cart removed_from_cart updated_wc_div', function() {
        // Sepet sayisini guncelle
        updateCartCount();
        // Mini cart icerigini guncelle
        updateMiniCart();
    });

    // Sepet sayisini guncelle
    function updateCartCount() {
        const ajaxUrl = typeof dmrthema_ajax !== 'undefined' ? dmrthema_ajax.ajax_url :
                       (typeof wc_add_to_cart_params !== 'undefined' ? wc_add_to_cart_params.ajax_url : '/wp-admin/admin-ajax.php');

        jQuery.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'get_cart_count'
            },
            success: function(response) {
                if (response.success) {
                    const cartCount = document.querySelector('.cart-count');
                    if (cartCount) {
                        cartCount.textContent = response.data.count;
                    }
                }
            }
        });
    }

    // Mini cart icerigini guncelle
    function updateMiniCart() {
        const ajaxUrl = typeof dmrthema_ajax !== 'undefined' ? dmrthema_ajax.ajax_url :
                       (typeof wc_add_to_cart_params !== 'undefined' ? wc_add_to_cart_params.ajax_url : '/wp-admin/admin-ajax.php');

        jQuery.ajax({
            url: ajaxUrl,
            type: 'POST',
            data: {
                action: 'get_mini_cart'
            },
            success: function(response) {
                if (response.success) {
                    const miniCartContent = document.querySelector('.widget_shopping_cart_content');
                    if (miniCartContent) {
                        miniCartContent.innerHTML = response.data.mini_cart;
                    }
                }
            }
        });
    }
});
