/**
 * Sepet Sidebar JavaScript
 */

// <PERSON><PERSON> yuklendiginde calistir
window.addEventListener('load', function() {
    // Elementleri bul
    const cartToggle = document.getElementById('cart-toggle');
    const cartSidebar = document.getElementById('cart-sidebar');
    const cartSidebarClose = document.getElementById('cart-sidebar-close');
    const cartSidebarOverlay = document.getElementById('cart-sidebar-overlay');

    // Sepet sidebar'ini ac
    function openCartSidebar() {
        if (cartSidebar && cartSidebarOverlay) {
            cartSidebar.classList.add('active');
            cartSidebarOverlay.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    // Sepet sidebar'ini kapat
    function closeCartSidebar() {
        if (cartSidebar && cartSidebarOverlay) {
            cartSidebar.classList.remove('active');
            cartSidebarOverlay.classList.remove('active');
            document.body.style.overflow = '';
        }
    }

    // Event listener'lar ekle
    if (cartToggle) {
        cartToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            openCartSidebar();
        });
    }

    if (cartSidebarClose) {
        cartSidebarClose.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeCartSidebar();
        });
    }

    if (cartSidebarOverlay) {
        cartSidebarOverlay.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeCartSidebar();
        });
    }

    // ESC tusuna basildiginda sidebar'i kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && cartSidebar && cartSidebar.classList.contains('active')) {
            closeCartSidebar();
        }
    });

    // WooCommerce sepet guncelleme olaylarini dinle
    if (typeof jQuery !== 'undefined') {
        // Sepete urun eklendiginde
        jQuery(document.body).on('added_to_cart', function() {
            // Sepet sayisini guncelle
            updateCartCount();
            // Sidebar'i ac
            openCartSidebar();
            // Sayfayi yenile (duplikasyon onlemek icin)
            setTimeout(() => {
                location.reload();
            }, 500);
        });

        // Sepetten urun cikarildiginda
        jQuery(document.body).on('removed_from_cart updated_wc_div', function() {
            // Sepet sayisini guncelle
            updateCartCount();
            // Sayfayi yenile
            setTimeout(() => {
                location.reload();
            }, 300);
        });
    }

    // Sepet sayisini guncelle
    function updateCartCount() {
        if (typeof jQuery === 'undefined') return;

        jQuery.post(dmrthema_ajax.ajax_url, {
            action: 'get_cart_count'
        }, function(response) {
            if (response.success) {
                const cartCount = document.querySelector('.cart-count');
                if (cartCount) {
                    cartCount.textContent = response.data.count;
                }
            }
        });
    }

    // Mini cart icerigini guncelle (artik sadece sayfa yenileme)
    function updateMiniCart() {
        // Bu fonksiyon artik kullanilmiyor,
        // cunku sepet guncellemelerinde direkt sayfa yenileniyor
        return;
    }
});


