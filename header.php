<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title( '|', true, 'right' ); ?></title>
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <div class="header-top">
        <div class="container">
            <div class="logo">
                <!-- Logo buraya gelecek -->
                <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home"><?php bloginfo( 'name' ); ?></a>
            </div>
            <div class="search-form-container">
                <?php get_search_form(); ?>
            </div>
            <div class="header-right">
                <div class="location">
                    <!-- Konum ikonu ve metni eklenebilir -->
                    <span>Konum</span>
                </div>
                <div class="user-actions">
                    <?php if ( is_user_logged_in() ) : ?>
                        <a href="<?php echo get_permalink( get_option('woocommerce_myaccount_page_id') ); ?>" class="my-account-button">Giriş Yap <span>veya üye ol</span></a>
                    <?php else : ?>
                        <a href="<?php echo get_permalink( get_option('woocommerce_myaccount_page_id') ); ?>" class="login-button">Giriş Yap <span>veya üye ol</span></a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="header-bottom">
        <div class="container">
            <nav class="main-navigation">
                <?php
                wp_nav_menu( array(
                    'theme_location' => 'primary',
                    'menu_id'        => 'primary-menu',
                    'walker'         => new Dmr_Walker_Nav_Menu(),
                ) );
                ?>
            </nav>
            <div class="cart">
                <button class="cart-button" id="cart-toggle">
                    <svg class="cart-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php if ( class_exists( 'WooCommerce' ) && WC()->cart ) : ?>
                        <span class="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                    <?php endif; ?>
                </button>
            </div>
        </div>
    </div>
</header>

<!-- Sepet Sidebar Overlay -->
<div class="cart-sidebar-overlay" id="cart-sidebar-overlay"></div>

<!-- Sepet Sidebar -->
<div id="cart-sidebar" class="cart-sidebar">
    <div class="cart-sidebar-content">
        <div class="cart-sidebar-header">
            <h3>Sepetim</h3>
            <button class="cart-sidebar-close" id="cart-sidebar-close">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
        <div class="cart-sidebar-body">
            <?php if ( class_exists( 'WooCommerce' ) ) : ?>
                <div class="widget_shopping_cart_content">
                    <ul class="woocommerce-mini-cart cart_list product_list_widget">
                        <?php
                        if ( ! WC()->cart->is_empty() ) :
                            foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) :
                                $_product   = apply_filters( 'woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key );
                                $product_id = apply_filters( 'woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key );

                                if ( $_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters( 'woocommerce_widget_cart_item_visible', true, $cart_item, $cart_item_key ) ) :
                                    $product_name      = apply_filters( 'woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key );
                                    $thumbnail         = apply_filters( 'woocommerce_cart_item_thumbnail', $_product->get_image(), $cart_item, $cart_item_key );
                                    $product_price     = apply_filters( 'woocommerce_cart_item_price', WC()->cart->get_product_price( $_product ), $cart_item, $cart_item_key );
                                    $product_permalink = apply_filters( 'woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink( $cart_item ) : '', $cart_item, $cart_item_key );
                                    ?>
                                    <li class="woocommerce-mini-cart-item <?php echo esc_attr( apply_filters( 'woocommerce_mini_cart_item_class', 'mini_cart_item', $cart_item, $cart_item_key ) ); ?>">
                                        <?php
                                        echo apply_filters( 'woocommerce_cart_item_remove_link', sprintf(
                                            '<a href="%s" class="remove remove_from_cart_button" aria-label="%s" data-product_id="%s" data-cart_item_key="%s" data-product_sku="%s">&times;</a>',
                                            esc_url( wc_get_cart_remove_url( $cart_item_key ) ),
                                            esc_html__( 'Remove this item', 'woocommerce' ),
                                            esc_attr( $product_id ),
                                            esc_attr( $cart_item_key ),
                                            esc_attr( $_product->get_sku() )
                                        ), $cart_item_key );
                                        ?>
                                        <?php if ( empty( $product_permalink ) ) : ?>
                                            <?php echo $thumbnail; ?>
                                        <?php else : ?>
                                            <a href="<?php echo esc_url( $product_permalink ); ?>">
                                                <?php echo $thumbnail; ?>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ( empty( $product_permalink ) ) : ?>
                                            <?php echo wp_kses_post( $product_name ); ?>
                                        <?php else : ?>
                                            <a href="<?php echo esc_url( $product_permalink ); ?>">
                                                <?php echo wp_kses_post( $product_name ); ?>
                                            </a>
                                        <?php endif; ?>
                                        <?php echo wc_get_formatted_cart_item_data( $cart_item ); ?>
                                        <?php echo apply_filters( 'woocommerce_widget_cart_item_quantity', '<span class="quantity">' . sprintf( '%s &times; %s', $cart_item['quantity'], $product_price ) . '</span>', $cart_item, $cart_item_key ); ?>
                                    </li>
                                    <?php
                                endif;
                            endforeach;
                        else : ?>
                            <li class="woocommerce-mini-cart__empty-message"><?php esc_html_e( 'No products in the cart.', 'woocommerce' ); ?></li>
                        <?php endif; ?>
                    </ul>
                </div>
            <?php else : ?>
                <p>WooCommerce eklentisi aktif degil.</p>
            <?php endif; ?>
        </div>

        <!-- Fixed Footer -->
        <?php if ( class_exists( 'WooCommerce' ) && ! WC()->cart->is_empty() ) : ?>
        <div class="cart-sidebar-footer">
            <div class="widget_shopping_cart_content">
                <p class="woocommerce-mini-cart__total total">
                    <strong><?php esc_html_e( 'Ara toplam', 'woocommerce' ); ?>: <?php echo WC()->cart->get_cart_subtotal(); ?></strong>
                </p>
                <p class="woocommerce-mini-cart__buttons buttons">
                    <a href="<?php echo esc_url( wc_get_cart_url() ); ?>" class="button wc-forward"><?php esc_html_e( 'Sepetim', 'woocommerce' ); ?></a>
                    <a href="<?php echo esc_url( wc_get_checkout_url() ); ?>" class="button checkout wc-forward"><?php esc_html_e( 'Odeme', 'woocommerce' ); ?></a>
                </p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>
